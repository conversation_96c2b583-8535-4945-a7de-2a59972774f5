Since this powers <PERSON>'s real email assistant, only contributions that improve that experience for <PERSON> will be merged in.

<PERSON> is very supportive of variants of this email assistant popping up. Add in other LLMs - great! Add in other email providers - great!

He just doesn't want to maintain them as he's already very busy.

Therefore, he will only merge in PRs that improve his experience, but he heartily invites you to fork and modify this repo to your hearts content!
