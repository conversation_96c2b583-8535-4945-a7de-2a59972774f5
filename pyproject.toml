[tool.poetry]
name = "eaia"
version = "0.1.0"
description = ""
authors = []
readme = "README.md"

[tool.poetry.dependencies]
python = "^3.11"
langgraph = "^0.4.5"
langgraph-checkpoint = "^2.0.0"
langchain = "^0.3.9"
langchain-openai = "^0.2"
langchain-anthropic = "^0.3"
google-api-python-client = "^2.128.0"
google-auth-oauthlib = "^1.2.0"
google-auth-httplib2 = "^0.2.0"
langgraph-sdk = "^0.1"
langsmith = "^0.2"
pytz = "*"
pyyaml = "*"
python-dateutil = "^2.9.0.post0"
python-dotenv = "^1.0.1"

[tool.setuptools.packages.find]
where = ["src"]

[tool.setuptools.package-data]
customer_support = ["*.txt", "*.rst"]

[tool.poetry.group.dev.dependencies]
ipykernel = "^6.29.4"
pytest-asyncio = "^0.23.6"
pytest = "^8.2.0"
pytest-watch = "^4.2.0"
vcrpy = "^6.0.1"
langgraph-cli = "^0.1.35"

[build-system]
requires = ["poetry-core"]
build-backend = "poetry.core.masonry.api"

[tool.pytest.ini_options]
asyncio_mode = "auto"
